use std::time::{SystemTime, UNIX_EPOCH};

use arrayvec::ArrayVec;
use base64::prelude::*;
use ed25519_dalek::{SigningKey, ed25519::signature::SignerMut, pkcs8::DecodePrivateKey};

use crate::{
    Currency, MAKER_FEE_INDEX, Message, ORDER_FILTER_MIN_ORDER_QTY_INDEX, ORDER_FILTERS,
    ORDER_QUANTITIES, PREDEFINED_RINGS, TAKER_FEE_INDEX, TRADING_FEES, TradingPair,
    engine::trading_pair::PRICE_TICK,
    net::{
        message::http::{HeaderMap, HttpRequest, Method},
        utils::circular_buffer::CircularBuffer,
    },
};

use super::trading_pair::LOT_SIZE_MULT;

/// 根据 price_tick 计算所需的小数位数
fn calculate_decimal_places(price_tick: f64) -> usize {
    if price_tick >= 1.0 {
        0
    } else {
        let mut places = 0;
        let temp = price_tick;
        while (temp * 10.0_f64.powi(places as i32)).fract().abs() > 1e-10 {
            places += 1;
            if places > 10 {
                break;
            } // 防止无限循环
        }
        places
    }
}

/// 格式化价格到指定的 price_tick 精度
fn format_price_with_tick(price: f64, price_tick: f64) -> String {
    // 调整价格到正确的精度
    let adjusted_price = (price / price_tick).round() * price_tick;

    // 计算所需的小数位数
    let decimal_places = calculate_decimal_places(price_tick);

    // 格式化价格到正确的小数位数
    format!("{:.prec$}", adjusted_price, prec = decimal_places)
}

// fn hmac_sign(query: &str, SECRET_KEY: &str) -> String {
//     let mut mac =
//         HmacSha256::new_from_slice(SECRET_KEY.as_bytes()).expect("HMAC can take key of any size");
//     mac.update(query.as_bytes());
//     hex::encode(mac.finalize().into_bytes())
// }

// API keys are now loaded from config.json via build.rs
use crate::engine::trading_pair::{API_KEY as AK, PRIVATE_KEY_PEM as SK_PEM};

fn ed25519_sign(query: &str) -> String {
    let mut signing_key = SigningKey::from_pkcs8_pem(SK_PEM).unwrap();
    let signature = signing_key.sign(query.as_bytes());
    BASE64_STANDARD.encode(signature.to_bytes())
}

pub fn generate_trading_fee_request() -> Message<'static> {
    let timestamp = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap()
        .as_millis() as u64;
    let ts_str = timestamp.to_string();
    let params = format!("timestamp={}", ts_str);
    let signature = ed25519_sign(params.as_str());
    let query = format!("{}&signature={}", params, signature);
    let uri = format!("/sapi/v1/asset/tradeFee?{}", query);
    let mut headers = HeaderMap::new();
    headers.add("X-MBX-APIKEY", AK);
    Message::HttpRequest(HttpRequest {
        method: Method::GET,
        headers: headers,
        uri: uri.leak(),
    })
}

pub fn update_trading_fee(data: &[u8]) {
    let json: serde_json::Value = serde_json::from_slice(data).expect("Invalid JSON format");
    for item in json.as_array().unwrap() {
        let symbol = item["symbol"].as_str().unwrap();
        let maker_fee = item["makerCommission"]
            .as_str()
            .unwrap()
            .parse::<f64>()
            .unwrap();
        let taker_fee = item["takerCommission"]
            .as_str()
            .unwrap()
            .parse::<f64>()
            .unwrap();
        let pair = TradingPair::from(symbol);
        if pair == TradingPair::Unknown {
            continue;
        }
        let pair_idx = pair as usize;
        unsafe {
            TRADING_FEES[pair_idx][MAKER_FEE_INDEX] = maker_fee;
            TRADING_FEES[pair_idx][TAKER_FEE_INDEX] = taker_fee;
        }
    }
}

#[inline(always)]
pub fn generate_order_requests<const C: usize>(
    ring_index: usize,
    now_in_ns: u64,
    buf: &mut CircularBuffer<C>,
    edge_index: usize,
) {
    let ring = PREDEFINED_RINGS[ring_index];
    let timestamp = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap()
        .as_millis() as u64;

    let mut order_id_buf = itoa::Buffer::new();
    let order_id_str = order_id_buf.format(now_in_ns);
    let mut ts_buf = itoa::Buffer::new();
    let ts_str = ts_buf.format(timestamp);

    let edge = ring[edge_index];
    let mut edge_index_buf = itoa::Buffer::new();
    let edge_index_str = edge_index_buf.format(edge_index);
    let mut ring_index_buf = itoa::Buffer::new();
    let ring_index_str = ring_index_buf.format(ring_index);

    let mutl = LOT_SIZE_MULT[edge.0 as usize];
    let mut quantity_buf = ArrayVec::<u8, 16>::new();
    let scaled = (unsafe { ORDER_QUANTITIES[edge_index] } * mutl as f64).floor() as u64;
    let mut it = itoa::Buffer::new();
    let digits = it.format(scaled).as_bytes();
    let scale = mutl.ilog10() as usize;

    if mutl == 1 {
        quantity_buf.try_extend_from_slice(digits).unwrap();
    } else if digits.len() <= scale {
        quantity_buf.try_extend_from_slice(b"0.").unwrap();
        for _ in 0..(scale - digits.len()) {
            quantity_buf.try_extend_from_slice(b"0").unwrap();
        }
        quantity_buf.try_extend_from_slice(digits).unwrap();
    } else {
        let int_len = digits.len() - scale;
        quantity_buf
            .try_extend_from_slice(&digits[..int_len])
            .unwrap();
        quantity_buf.try_extend_from_slice(b".").unwrap();
        quantity_buf
            .try_extend_from_slice(&digits[int_len..])
            .unwrap();
    }

    // 计算限价单价格
    // let price = unsafe {
    //     match edge.1 {
    //         EdgeDirection::Forward => {
    //             // 买入时使用 ask 价格（稍微提高以确保成交）
    //             1.0 / TRADING_PAIR_RATES[edge.0 as usize][EdgeDirection::Forward as usize]
    //         }
    //         EdgeDirection::Reverse => {
    //             // 卖出时使用 bid 价格（稍微降低以确保成交）
    //             TRADING_PAIR_RATES[edge.0 as usize][EdgeDirection::Reverse as usize]
    //         }
    //     }
    // };

    // 获取价格精度并格式化价格
    // let price_tick = ORDER_FILTERS[edge.0 as usize][ORDER_FILTER_PRICE_TICK_INDEX];
    // let price_str = format_price_with_tick(price, price_tick);
    // let mut price_buf = ArrayVec::<u8, 32>::new();
    // price_buf
    //     .try_extend_from_slice(price_str.as_bytes())
    //     .unwrap();

    let mut json = ArrayVec::<u8, 256>::new();
    json.try_extend_from_slice(br#"{"id":""#).unwrap();
    json.try_extend_from_slice(order_id_str.as_bytes()).unwrap();
    json.try_extend_from_slice(br#"","method":"order.place","params":{"symbol":""#)
        .unwrap();
    json.try_extend_from_slice(edge.0.to_str().as_bytes())
        .unwrap();
    json.try_extend_from_slice(br#"","side":""#).unwrap();
    json.try_extend_from_slice(edge.1.to_str().as_bytes())
        .unwrap();
    // json.try_extend_from_slice(br#"","timeInForce":"IOC"#)
    // .unwrap();
    json.try_extend_from_slice(br#"","type":"MARKET","newClientOrderId":""#)
        .unwrap();
    json.try_extend_from_slice(order_id_str.as_bytes()).unwrap();
    json.push(b'-');
    json.try_extend_from_slice(ring_index_str.as_bytes())
        .unwrap();
    json.push(b'-');
    json.try_extend_from_slice(edge_index_str.as_bytes())
        .unwrap();
    json.push(b'-');
    json.push(b'0');
    // json.try_extend_from_slice(br#"","price":""#).unwrap();
    // json.try_extend_from_slice(price_buf.as_slice()).unwrap();
    json.try_extend_from_slice(br#"","quantity":""#).unwrap();
    json.try_extend_from_slice(quantity_buf.as_slice()).unwrap();
    json.try_extend_from_slice(br#"","timestamp":"#).unwrap();
    json.try_extend_from_slice(ts_str.as_bytes()).unwrap();
    json.try_extend_from_slice(br#"}}"#).unwrap();

    let payload_len = json.len();
    let fin_txt = 0x81u8;
    let mut header = [0u8; 8];
    let mut header_len = 6;
    header[0] = fin_txt;
    let mut second = 0x80u8;
    match payload_len {
        len if len < 126 => {
            second |= len as u8;
        }
        len if len <= 65535 => {
            second |= 126;
            let len_bytes = (payload_len as u16).to_be_bytes();
            header[2..4].copy_from_slice(&len_bytes);
            header_len = 8;
        }
        len => {
            panic!("payload_len too long: {}", len);
        }
    }
    header[1] = second;

    let mask_u32 = now_in_ns as u32;
    let mask_bytes = mask_u32.to_be_bytes();
    match header_len {
        6 => {
            buf.put(&header[..2]);
        }
        8 => {
            buf.put(&header[..4]);
        }
        _ => {
            panic!("header_len: {}", header_len);
        }
    }
    buf.put(&mask_bytes[..4]);

    for (i, byte) in json.iter().enumerate() {
        buf.put_u8(byte ^ mask_bytes[i & 3]);
    }
}

pub fn generate_order_request_by_symbol<const N: usize>(
    now_in_ns: u64,
    buf: &mut CircularBuffer<N>,
    price: f64,
    symbol: &str,
) {
    let price = price * 0.98;
    let timestamp = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap()
        .as_millis() as u64;
    let mut order_id_buf = itoa::Buffer::new();
    let order_id_str = order_id_buf.format(now_in_ns);
    let mut ts_buf = itoa::Buffer::new();
    let ts_str = ts_buf.format(timestamp);

    let pair = TradingPair::from(symbol);

    let mut edge_index_buf = itoa::Buffer::new();
    let edge_index_str = edge_index_buf.format(0);
    let mut ring_index_buf = itoa::Buffer::new();
    let ring_index_str = ring_index_buf.format(0);
    let price_tick = PRICE_TICK[pair as usize];
    let price_str = format_price_with_tick(price, price_tick);

    let mutl = LOT_SIZE_MULT[pair as usize];
    let min_qty = ORDER_FILTERS[pair as usize][ORDER_FILTER_MIN_ORDER_QTY_INDEX];
    let quote = pair.quote();
    let quote_qty = match quote {
        Currency::XETH => 0.002f64,
        // Currency::XBTC => 0.0001f64,
        _ => 20.0f64,
    };
    let national_qty = quote_qty / price;
    let qty = if national_qty < min_qty {
        min_qty
    } else {
        national_qty
    };
    let mut quantity_buf = CircularBuffer::<32>::new();
    let scaled = (qty * mutl as f64).floor() as u64;
    let mut it = itoa::Buffer::new();
    let digits = it.format(scaled).as_bytes();
    let scale = mutl.ilog10() as usize;

    if mutl == 1 {
        quantity_buf.put(digits);
    } else if digits.len() <= scale {
        quantity_buf.put(b"0.");
        for _ in 0..(scale - digits.len()) {
            quantity_buf.put(b"0");
        }
        quantity_buf.put(digits);
    } else {
        let int_len = digits.len() - scale;
        quantity_buf.put(&digits[..int_len]);
        quantity_buf.put(b".");
        quantity_buf.put(&digits[int_len..]);
    }

    let mut json = CircularBuffer::<512>::new();
    json.put(br#"{"id":""#);
    json.put(order_id_str.as_bytes());
    json.put(br#"","method":"order.place","params":{"symbol":""#);
    json.put(symbol.as_bytes());
    json.put(br#"","side":""#);
    json.put(b"BUY");
    json.put(br#"","timeInForce":"IOC"#);
    json.put(br#"","type":"LIMIT","newClientOrderId":""#);
    json.put(order_id_str.as_bytes());
    json.put(b"-");
    json.put(ring_index_str.as_bytes());
    json.put(b"-");
    json.put(edge_index_str.as_bytes());
    json.put(b"-");
    json.put(b"1");
    json.put(br#"","price":""#);
    json.put(price_str.as_bytes());
    json.put(br#"","quantity":""#);
    json.put(quantity_buf.as_slices().0);
    json.put(br#"","timestamp":"#);
    json.put(ts_str.as_bytes());
    json.put(br#"}}"#);

    let payload_len = json.len();
    let fin_txt = 0x81u8;
    let mut header = [0u8; 8];
    let mut header_len = 6;
    header[0] = fin_txt;
    let mut second = 0x80u8;
    match payload_len {
        len if len < 126 => {
            second |= len as u8;
        }
        len if len <= 65535 => {
            second |= 126;
            let len_bytes = (payload_len as u16).to_be_bytes();
            header[2..4].copy_from_slice(&len_bytes);
            header_len = 8;
        }
        len => {
            panic!("payload_len too long: {}", len);
        }
    }
    header[1] = second;

    let mask_u32 = now_in_ns as u32;
    let mask_bytes = mask_u32.to_be_bytes();
    match header_len {
        6 => {
            buf.put(&header[..2]);
        }
        8 => {
            buf.put(&header[..4]);
        }
        _ => {
            panic!("header_len: {}", header_len);
        }
    }
    buf.put(&mask_bytes[..4]);

    for (i, byte) in json.as_slices().0.iter().enumerate() {
        buf.put_u8(byte ^ mask_bytes[i & 3]);
    }
}

pub fn generate_user_data_sub_request() -> String {
    r#"
{
  "id": "d3df8a21-98ea-4fe0-8f4e-0fcea5d418b7",
  "method": "userDataStream.subscribe"
}
"#
    .to_string()
}

pub fn generate_session_logon_request() -> String {
    let timestamp = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap()
        .as_millis() as u64;
    let ts_str = timestamp.to_string();
    let mut params = vec![("apiKey", AK), ("timestamp", ts_str.as_str())];
    params.sort_by(|a, b| a.0.cmp(b.0));
    let param_str = params
        .iter()
        .map(|(k, v)| format!("{}={}", k, v))
        .collect::<Vec<_>>()
        .join("&");
    let signature = ed25519_sign(&param_str);
    let request = format!(
        r#"
{{
  "id": "56374a46-3061-486b-a311-99ee972eb648",
  "method": "session.logon",
  "params": {{
    "apiKey": "{}",
    "signature": "{}",
    "timestamp": {}
    }}
}}
"#,
        AK, signature, timestamp
    );
    request.to_string()
}
