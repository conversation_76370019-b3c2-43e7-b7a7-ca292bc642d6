use libwebsocket_rs::{flush_logs, logln};

fn main() {
    println!("Testing logger - writing to file...");

    logln!("Test message 1: Hello from logger!");
    logln!("Test message 2: Current timestamp test");
    logln!(
        "Test message 3: Performance test with numbers: {}, {}, {}",
        123,
        456.789,
        true
    );

    // Test buffer flushing
    flush_logs!();

    println!("Logger test completed. Check for .log file in current directory.");
}
